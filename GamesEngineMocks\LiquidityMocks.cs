﻿using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngineMocks
{
    public class LiquidityMocks
    {
        public static void Init(Actor actor)
        {
            var mock = new LiquidityMockGenerator(actor);
        }

        public static void SomeDepositsAndTanksWithOn<PERSON><PERSON><PERSON><PERSON><PERSON>(Actor actor)
        {
            // 1. Create the base mock with minimal setup
            var mock = new LiquidityMockGenerator(actor);

            // 2. Create and confirm deposits for May
            var mayDeposit1 = mock.CreateDeposit(new DateTime(2024, 5, 10), "inv-001", 1001, 0.1m);
            var mayDeposit2 = mock.CreateDeposit(new DateTime(2024, 5, 15), "inv-002", 1002, 0.2m);
            mock.ConfirmDeposit(mayDeposit1);
            mock.ConfirmDeposit(mayDeposit2);

            // 3. Create and confirm deposits for June
            var juneDeposit1 = mock.CreateDeposit(new DateTime(2024, 6, 5), "inv-003", 1003, 0.15m);
            var juneDeposit2 = mock.CreateDeposit(new DateTime(2024, 6, 12), "inv-004", 1004, 0.25m);
            mock.ConfirmDeposit(juneDeposit1);
            mock.ConfirmDeposit(juneDeposit2);

            // 4. Create and confirm deposits for July
            var julyDeposit1 = mock.CreateDeposit(new DateTime(2024, 7, 1), "inv-005", 1005, 0.3m);
            var julyDeposit2 = mock.CreateDeposit(new DateTime(2024, 7, 20), "inv-006", 1006, 0.4m);
            mock.ConfirmDeposit(julyDeposit1);
            mock.ConfirmDeposit(julyDeposit2);

            // 5. Create tanks with specific creation dates and deposits
            var tankMay = mock.CreateTank("Alpha-Tank-May", "Contains May deposits", new DateTime(2024, 5, 20), new List<int> { mayDeposit1, mayDeposit2 });
            var tankJune = mock.CreateTank("Beta-Tank-June", "Contains June deposits", new DateTime(2024, 6, 25), new List<int> { juneDeposit1, juneDeposit2 });
            var tankJuly = mock.CreateTank("Gamma-Tank-July", "Contains July deposits", new DateTime(2024, 7, 25), new List<int> { julyDeposit1, julyDeposit2 });

            // 6. Create a single tanker containing all tanks
            var tankerId = mock.CreateTanker("Multi-Month Transport", "A tanker for Alpha, Beta, and Gamma tanks", new List<int> { tankMay, tankJune, tankJuly });

            // 7. Create new deposits that will remain in the Jar
            var augDeposit1 = mock.CreateDeposit(new DateTime(2024, 8, 1), "inv-007", 1007, 0.5m);
            var augDeposit2 = mock.CreateDeposit(new DateTime(2024, 8, 15), "inv-008", 1008, 0.6m);
            mock.ConfirmDeposit(augDeposit1);
            mock.ConfirmDeposit(augDeposit2);

            // 8. Add these new, unassigned deposits to the tanker's Root Tank
            mock.AddDepositsToTanker(tankerId, new List<int> { augDeposit1, augDeposit2 });
        }

        public static void SomeWithdrawalsInInboxAndSomeDispensersWithWithdrawals(Actor actor)
        {
            // 1. Create the base mock with minimal setup.
            var mock = new LiquidityMockGenerator(actor);

            // 2. Create some withdrawals for May 2024.
            mock.CreateWithdrawal(new DateTime(2024, 5, 12, 10, 30, 0), 0.05m, "pp_may_1", 2001, "dest_addr_may1", "at_addr_1", "ext_ref_may1");
            mock.CreateWithdrawal(new DateTime(2024, 5, 25, 15, 0, 0), 0.10m, "pp_may_2", 2002, "dest_addr_may2", "at_addr_2", "ext_ref_may2");

            // 3. Create some withdrawals for June 2024.
            mock.CreateWithdrawal(new DateTime(2024, 6, 8, 9, 0, 0), 0.02m, "pp_june_1", 2003, "dest_addr_june1", "at_addr_3", "ext_ref_june1");
            mock.CreateWithdrawal(new DateTime(2024, 6, 20, 18, 45, 0), 0.08m, "pp_june_2", 2004, "dest_addr_june2", "at_addr_4", "ext_ref_june2");

            // 4. Create some withdrawals for July 2024.
            mock.CreateWithdrawal(new DateTime(2024, 7, 5, 11, 20, 0), 0.15m, "pp_july_1", 2005, "dest_addr_july1", "at_addr_5", "ext_ref_july1");

            // 1. Create a few withdrawals, they will land in the inbox first.
            mock.CreateWithdrawal(new DateTime(2024, 8, 1), 0.1m, "pp_w1", 3001, "dest1", "at1", "ext1"); // ID will be 6
            mock.CreateWithdrawal(new DateTime(2024, 8, 2), 0.2m, "pp_w2", 3002, "dest2", "at2", "ext2"); // ID will be 7
            mock.CreateWithdrawal(new DateTime(2024, 8, 3), 0.3m, "pp_w3", 3003, "dest3", "at3", "ext3"); // ID will be 8
            mock.CreateWithdrawal(new DateTime(2024, 8, 4), 0.4m, "pp_w4", 3004, "dest4", "at4", "ext4"); // ID will be 9
            mock.CreateWithdrawal(DateTime.Now, 0.5m, "pp_w5", 3004, "dest5", "at5", "ext5");

            // 2. Create two different dispensers.
            var dailyDispenserId = mock.CreateDispenser(new DateTime(2024, 8, 1), "Daily Payouts", "Disperser for daily withdrawals");
            var weeklyDispenserId = mock.CreateDispenser(new DateTime(2024, 8, 1), "Weekly Payouts", "Disperser for weekly batch");

            // 3. Move withdrawals from the inbox to the new dispensers.
            mock.MoveWithdrawalToDispenser(6, dailyDispenserId);
            mock.MoveWithdrawalToDispenser(7, dailyDispenserId);
            mock.MoveWithdrawalToDispenser(8, weeklyDispenserId);
        }

        public static void TankWithMultipleVersions(Actor actor)
        {
            var mock = new LiquidityMockGenerator(actor);

            // --- Initial State ---
            // Create some deposits that will be used across different versions
            var deposit1 = mock.CreateDeposit(new DateTime(2024, 1, 1), "inv-v1", 2001, 0.1m);
            var deposit2 = mock.CreateDeposit(new DateTime(2024, 1, 2), "inv-v2", 2002, 0.2m);
            mock.ConfirmDeposit(deposit1);
            mock.ConfirmDeposit(deposit2);

            // --- Version 1 ---
            // Create the initial tank with the first two deposits.
            var tankId = mock.CreateTank("Versioned Tank", "Initial version of the tank", new DateTime(2024, 1, 10), new List<int> { deposit1, deposit2 });

            // --- Version 2 ---
            // Create a new deposit and move it from the Jar into the tank. This creates a new version.
            var deposit3 = mock.CreateDeposit(new DateTime(2024, 2, 1), "inv-v3", 2003, 0.3m);
            mock.ConfirmDeposit(deposit3);
            mock.MoveDepositsToTank(tankId, new List<int> { deposit3 });

            // --- Version 3 ---
            // Create another deposit and move it as well, creating a third version.
            var deposit4 = mock.CreateDeposit(new DateTime(2024, 2, 5), "inv-v4", 2004, 0.4m);
            mock.ConfirmDeposit(deposit4);
            mock.MoveDepositsToTank(tankId, new List<int> { deposit4 });

            // --- Version 4 ---
            // Create a separate, empty tank and then add it as a child to our main tank. This creates a fourth version.
            var childTankId = mock.CreateEmptyTank("Child Tank", "A child container", new DateTime(2024, 3, 1));
            mock.MoveTankToTank(tankId, new List<int> { childTankId });
        }
    }
}
