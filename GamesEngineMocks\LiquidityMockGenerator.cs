﻿using GamesEngine.Finance;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineMocks
{
    public class LiquidityMockGenerator:Mock
    {
        public Actor actor;
        private readonly string _defaultDomain = "localhost";
        private readonly string _defaultKind = "BTC";

        public String Perform(String script)
        {
            string result;
            try
            {
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
                result = actor.PerformCmdAsync(script, IpAddress.DEFAULT, UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
                result = CheckForAssert(result);
            }
            catch (Exception e)
            {
                throw e.InnerException;
            }
            return result;
        }

        public LiquidityMockGenerator(Actor actor)
        {
            this.actor = actor;
            Perform($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;

                if (!company.System.Coins.ExistsIsoCode('FP'))
				{{
					coin = company.System.Coins.Add(0, 'FP', 'FP', 2, 'F', 'free play', {CoinType.Digital});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('USD'))
				{{
					coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('BTC'))
				{{
					company.System.Coins.Add(3, 'BTC', 'BTC', 8, '₿', 'bitcoin', {CoinType.Crypt});
				}}
				if (!company.System.Coins.ExistsIsoCode('ETH'))
				{{
					company.System.Coins.Add(7, 'ETH', 'ETH', 8, 'Ξ', 'ethereum', {CoinType.Crypt});
				}}

                store = company.Sales.CreateStore(8,'Rubicon');
				store.Alias = 'rubicon';
				store.MakeCurrent();
                domain = company.Sales.CreateDomain(false, 1, '{_defaultDomain}', {Agents.INSIDER});
				company.Sales.CurrentStore.Add(domain);

                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
                liquid.ParentFlow.AddPaymentDock(domain, 'ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag');
                xpub = Xpub('xpub661MyMwAqRbcFtXgS5sYJABqqG9YLmC4Q1Rdap9gSE8NqtwybGhePY2gZ29ESFjqJoCu1Rupje8YtGqsefD265TMg7usUDFdp6W1EGMcet8');
                source = liquid.AddSource(xpub);
			");
        }

        public int CreateDeposit(DateTime createdAt, string invoiceId, int externalRef, decimal amount, decimal rate = 50000.0m)
        {
            string script = $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
                source = liquid.Source;
                domain = company.Sales.DomainFrom('{_defaultDomain}');

                Eval('depositId = '+source.NextDepositId()+';');
                deposit = source.CreateDraftDeposit(itIsThePresent, {createdAt:M/d/yyyy H:m:s}, depositId, '{invoiceId}', {externalRef}, 'A670582', {externalRef}, 'dest-addr', {amount}, {rate}, 'USD', {amount * rate}, 3, 8, domain);
                
                print deposit.Id depositId;
            }}";

            var jsonResult = Perform(script);
            var response = JsonConvert.DeserializeObject<IdResponse>(jsonResult);
            return response.DepositId.Value;
        }

        public void ConfirmDeposit(int depositId)
        {
            string script = $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
                source = liquid.Source;
                deposit = source.Jar.FindDepositById({depositId});
                source.ConfirmDeposit(itIsThePresent, Now, deposit);
            }}";
            Perform(script);
        }

        public int CreateTank(string name, string description, DateTime createdAt, List<int> depositIds)
        {
            if (depositIds == null || !depositIds.Any())
                throw new ArgumentException("At least one deposit ID is required to create a tank.");

            var depositIdList = string.Join(",", depositIds);

            string script = $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
                source = liquid.Source;
                Eval('tankId = '+source.NextTankId()+';');
                Eval('newJarVersion = '+source.NextJarVersion()+';');
                tank = source.Jar.CreateTank(itIsThePresent, {createdAt:M/d/yyyy H:m:s}, tankId, '{name}', '{description}', newJarVersion, {{{depositIdList}}});
                print tank.Id tankId;
            }}";

            var jsonResult = Perform(script);
            var response = JsonConvert.DeserializeObject<IdResponse>(jsonResult);
            return response.TankId.Value;
        }

        public int CreateTanker(string name, string description, List<int> tankIds)
        {
            if (tankIds == null || !tankIds.Any())
                throw new ArgumentException("At least one tank ID is required to create a tanker.");

            var tankIdList = string.Join(",", tankIds);

            string script = $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
                source = liquid.Source;
                Eval('tankerId = '+source.NextTankerId()+';');
                tanker = source.CreateTanker(itIsThePresent, Now, tankerId, '{name}', '{description}', {{{tankIdList}}});
                print tanker.Id tankerId;
            }}";

            var jsonResult = Perform(script);
            var response = JsonConvert.DeserializeObject<IdResponse>(jsonResult);
            return response.TankerId.Value;
        }

        public void AddDepositsToTanker(int tankerId, List<int> depositIds)
        {
            if (depositIds == null || !depositIds.Any()) return;

            var depositIdList = string.Join(",", depositIds);

            string script = $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
                source = liquid.Source;
                tanker = source.FindTanker({tankerId});
                tanker.AddDepositsByIds({{{depositIdList}}});
            }}";

            Perform(script);
        }

        public int CreateEmptyTank(string name, string description, DateTime createdAt)
        {
            string script = $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
                source = liquid.Source;
                Eval('tankId = '+source.NextTankId()+';');
                Eval('newJarVersion = '+source.NextJarVersion()+';');
                tank = source.Jar.CreateEmptyTank(itIsThePresent, {createdAt:M/d/yyyy H:m:s}, tankId, '{name}','{description}', newJarVersion);
                print tank.Id tankId;
            }}";

            var jsonResult = Perform(script);
            var response = JsonConvert.DeserializeObject<IdResponse>(jsonResult);
            return response.TankId.Value;
        }

        public void MoveDepositsToTank(int tankId, List<int> depositIds)
        {
            var depositIdList = string.Join(",", depositIds);
            string script = $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
                source = liquid.Source;
                Eval('newJarVersion = '+source.NextJarVersion()+';');
                source.Jar.MoveToTank(itIsThePresent, Now, newJarVersion, {tankId}, {{{depositIdList}}});
            }}";
            Perform(script);
        }

        public void MoveTankToTank(int parentTankId, List<int> childTankIds)
        {
            var childTankIdList = string.Join(",", childTankIds);
            string script = $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
                source = liquid.Source;
                tank = source.FindTank({parentTankId});
                tank.MoveToTanks({{{childTankIdList}}});
            }}";
            Perform(script);
        }

        public void CreateWithdrawal(DateTime createdAt, decimal amount, string pullPaymentId, int authorization, string destination, string atAddress, string externalReference)
        {
            decimal rate = 105234m;
            string script = $@"
            {{
                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
                outlet = liquid.Outlet;
                domain = company.Sales.DomainFrom('{_defaultDomain}');

                Eval('withdrawalId = '+outlet.NextWithdrawalId()+';');
                withdrawal = outlet.CreateWithdrawal(
                    itIsThePresent,
                    {createdAt:M/d/yyyy H:m:s},
                    withdrawalId,
                    '{pullPaymentId}',
                    {authorization},
                    {amount},
                    '{destination}',
                    '{atAddress}',
                    domain,
                    8,  
                    '{externalReference}',
                    105234.0,
                    {amount * rate}
                );
            }}";

            Perform(script);
        }

        public int CreateDispenser(DateTime createdAt, string name, string description)
        {
            string script = $@"
        {{
            liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
            outlet = liquid.Outlet;
            Eval('dispenserId = '+outlet.NextDispenserId()+';');
            dispenser = outlet.CreateDispenser(itIsThePresent, {createdAt:M/d/yyyy H:m:s}, dispenserId, '{name}', '{description}', {createdAt:M/d/yyyy H:m:s});
            
            print dispenser.Id dispenserId;
        }}";
            var jsonResult = Perform(script);
            var response = JsonConvert.DeserializeObject<IdResponse>(jsonResult);
            return response.DispenserId.Value;
        }

        public void MoveWithdrawalToDispenser(int withdrawalId, int dispenserId)
        {
            string script = $@"
        {{
            liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, Now, '{_defaultKind}');
            inbox = liquid.Outlet.DispenserInbox;
            
            foundWithdrawal = null;
            for (w : inbox.Withdrawals)
            {{
                if (w.Id == {withdrawalId})
                {{
                    foundWithdrawal = w;
                }}
            }}

            if (foundWithdrawal != null)
            {{
                inbox.MoveWithdrawal({dispenserId}, foundWithdrawal);
            }}
            else
            {{
                Assert(false, 'Could not find withdrawal with ID {withdrawalId} in the inbox.');
            }}
        }}";
            Perform(script);
        }
        internal class IdResponse
        {
            [JsonProperty("depositId")]
            public int? DepositId { get; set; }

            [JsonProperty("tankId")]
            public int? TankId { get; set; }

            [JsonProperty("tankerId")]
            public int? TankerId { get; set; }

            [JsonProperty("dispenserId")] 
            public int? DispenserId { get; set; }
        }
    }
}
